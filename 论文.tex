```latex
\documentclass[hyperref,UTF8,zihao=-4]{ctexart}
\usepackage{geometry}
\linespread{1.6}
\usepackage{fancyhdr}
\usepackage{amsmath}
\usepackage{graphicx}
\usepackage{float}
\usepackage{indentfirst}
\usepackage[colorlinks,linkcolor=black,anchorcolor=black,citecolor=black]{hyperref}
\usepackage{amsfonts}
\usepackage{booktabs}
\usepackage{tabularx}
\usepackage{algorithm}
\usepackage{algorithmic}
\setmainfont{SimSun}
\usepackage{algorithmicx}
\usepackage[noend]{algpseudocode} 


% 设置页面尺寸和页边距
\geometry{a4paper,
left=2.5cm,
right=2.5cm,
top=2.5cm,
bottom=2.5cm}

% 禁用CTeX宏包的章节标题宏，以便自定义
\ctexset{
section={
  format=\Large\bfseries,
  afterskip=1ex,
  afterindent=false,
},
subsection={
  format=\large\bfseries,
  afterskip=0.5ex,
  afterindent=false,
},
subsubsection={
  format=\normalsize\bfseries,
  afterskip=0.5ex,
  afterindent=false,
}}

% 设置标题和日期
\title{\bfseries\zihao{2} 无创产前检测中的数据驱动优化与诊断研究}
\date{} % 移除日期

% 设置页码
\pagestyle{plain} % 默认页码在底部居中

\begin{document}

\maketitle

% 摘要和关键词
\begin{center}\textbf{\Large 摘要}\end{center}
\ctexset{abstractname={}}
\begin{abstract}
本文针对无创产前检测（NIPT）中的胎儿Y染色体浓度分析与异常判定问题，采用GAMLSS模型揭示其非线性与异方差特性，构建了精准的关系模型，为后续时点优化奠定基础。基于BMI的简化GAMLSS与分位数回归相结合，优化了男胎NIPT检测时点，显著降低检测失败与延误风险，适用于临床个性化检测指导。通过综合多因素的GAMLSS模型与损失函数框架，进一步优化了检测时点，提出稳健的孕妇分组推荐方案，为临床决策提供量化依据。针对女胎异常判定，设计了两阶段诊断流程，结合动态阈值逻辑回归与一对多分类器，实现高召回率筛查与精准分型，为罕见病检测提供了可操作的临床路径。
\vspace{1ex}

\noindent\bfseries{关键词：}\normalfont{} NIPT；GAMLSS；决策理论；损失函数；可解释性机器学习
\end{abstract}
\newpage

% --- Chapter 1: Problem Restatement ---
\section*{第一章 问题重述}
\setcounter{section}{1}

\subsection*{1.1 研究背景}
无创产前检测（Non-Invasive Prenatal Testing, NIPT）通过分析母体血浆中的胎儿游离DNA（cffDNA），为早期筛查胎儿染色体非整倍体（如唐氏综合征）提供了高效手段。然而，NIPT的精准性受孕妇个体差异（如BMI、年龄、孕周）与检测时机的复杂交互影响，需在“尽早检测”与“确保准确性”间权衡。此外，女胎缺乏Y染色体作为直接指标，异常判定的统计挑战尤为严峻，尤其在样本稀缺的情况下。本研究通过数据驱动方法，系统解决NIPT检测中的规律发现、时点优化与异常诊断问题，为临床实践提供科学依据。

\subsection*{1.2 问题重述}
本次研究基于某地区NIPT数据集，针对以下四个核心任务展开：
\begin{enumerate}
    \item \textbf{关系特性分析}：采用广义可加模型（GAM）与位置、尺度、形状广义可加模型（GAMLSS），分析Y染色体浓度与孕周、BMI等因素的非线性与异方差关系，构建并检验显著性模型，为时点优化提供依据。结果揭示了孕周驱动的非单调正相关与BMI的负相关特性，为后续决策奠定基础。
    \item \textbf{基准时点决策}：基于BMI分组，利用简化GAMLSS与分位数回归，优化男胎NIPT检测时点，最小化检测失败与延误风险，提出分组推荐方案，为临床提供初步指导。结果表明早期检测可显著降低风险，为综合优化铺垫。
    \item \textbf{综合时点优化}：综合孕周、BMI、年龄、IVF妊娠等因素，通过完全版GAMLSS与损失函数框架，优化检测时点并进行误差分析，提出稳健的分组推荐方案，为个性化临床决策提供支持。结果提升了推荐方案的精度与稳定性。
    \item \textbf{女胎异常判定}：针对21、18、13号染色体非整倍体，设计两阶段诊断流程（高灵敏度筛查+特异性分型），结合动态阈值逻辑回归与一对多分类器，实现高召回率与精准分型，为罕见病检测提供临床路径。结果验证了流程的有效性与实用性。
\end{enumerate}

% --- Chapter 2: Problem Analysis ---
\section*{第二章 问题分析与技术路线}
\setcounter{section}{2}

\subsection*{2.1 问题分析}
NIPT检测的核心挑战包括：（1）Y染色体浓度受孕周、BMI等多因素非线性与异方差性影响，传统模型难以准确刻画；（2）检测时点需权衡“尽早发现”与“准确性”，涉及复杂风险优化；（3）女胎异常判定面临样本稀缺与类别不平衡，需兼顾高召回率与特异性。本文通过数据驱动方法，系统解决这些挑战。

\subsection*{2.2 技术路线}
研究遵循“规律发现—风险决策—智能诊断”的逻辑递进：
\begin{itemize}
    \item \textbf{问题一（关系特性分析）}：通过探索性数据分析（EDA）揭示变量关系，采用GAM捕捉非线性趋势，升级至GAMLSS建模均值与方差，得出Y浓度动态规律，为时点优化提供基础。结果验证了非线性与异方差特性，优于传统线性回归。
    \item \textbf{问题二与三（时点优化）}：基于简化GAMLSS（问题二）与完全版GAMLSS（问题三），结合损失函数优化检测时点，提出分组推荐方案。相较传统固定阈值法，本方法通过量化风险显著提高决策精度，为临床个性化检测提供支持。
    \item \textbf{问题四（女胎异常判定）}：设计两阶段诊断流程，高灵敏度逻辑回归筛查异常，特异性一对多分类器精准分型，解决小样本不平衡问题。相较单一多分类模型，本方法在召回率与分型精度上均有提升，为罕见病检测奠定基础。
\end{itemize}

\subsection*{2.3 方法比较与创新}
相较传统方法（如线性回归、固定阈值分类），本文的GAMLSS模型通过联合建模均值与方差，显著提升了Y浓度预测的准确性（AIC=-4422 vs. 线性回归约-4200）。损失函数框架优于概率阈值法，降低了10-15周的检测风险。两阶段诊断流程在召回率（95.6\%）上优于传统多分类模型（约85\%）。建议将模型整合至CDSS，优化NIPT临床应用。

% --- Chapter 3: Model Assumptions and Notation ---
\section*{第三章 模型假设与符号说明}
\setcounter{section}{3}

\subsection*{3.1 模型假设}
为确保模型科学性与适用性，本研究基于以下假设：
\begin{enumerate}
    \item \textbf{数据真实性}：预处理后的数据集准确反映临床NIPT检测结果，验证通过数据一致性分析（冲突比例15.2\%）。
    \item \textbf{分布合理性}：Y染色体浓度符合Beta分布族，GAMLSS模型通过残差诊断（Worm Plot）验证分布假设。
    \item \textbf{损失函数代表性}：检测失败与延误诊断的损失函数权重合理，基于临床文献设定（$C_{fail}=100$），经敏感性分析验证。
    \item \textbf{特征充分性}：Z值、GC含量、BMI等特征包含女胎异常判定的核心信息，通过SHAP值分析确认特征重要性。
\end{enumerate}

\subsection*{3.2 符号说明}
主要符号说明见表3.1。

\begin{table}[H]
    \centering
    \caption{主要符号说明}
    \label{tab:notation}
    \begin{tabularx}{\textwidth}{lX}
        \toprule
        \textbf{符号} & \textbf{说明} \\
        \midrule
        Y-cffDNA & 胎儿Y染色体浓度（Y染色体游离DNA比例） \\
        GAM & 广义可加模型 (Generalized Additive Model) \\
        GAMLSS & 位置、尺度、形状的广义可加模型 \\
        $\mu$ & GAMLSS模型中的均值（位置）参数 \\
        $\sigma$ & GAMLSS模型中的标准差/方差（尺度）参数 \\
        $s(\cdot)$ & 三次样条平滑函数 \\
        $te(\cdot)$ & 张量积平滑函数（用于交互项） \\
        $w$ & NIPT检测时点（孕周） \\
        $L(w)$ & 在孕周$w$进行检测的总损失函数 \\
        $P_{fail}(w)$ & 在孕周$w$进行检测的失败概率（Y浓度<4\%） \\
        $C_{fail}$ & 检测失败的成本权重 \\
        $C_{delay}(w)$ & 孕周$w$对应的延误诊断成本函数 \\
        One-vs-Rest & “一对多”分类策略 \\
        T13/T18/T21 & 13/18/21三体综合征 \\
        AIC/BIC & 赤池信息准则/贝叶斯信息准则 \\
        SHAP & SHapley Additive exPlanations，用于模型解释 \\
        DCA & 决策曲线分析 (Decision Curve Analysis) \\
        \bottomrule
    \end{tabularx}
\end{table}

\subsection*{3.3 假设检验与模型验证}
各假设通过以下方法验证：数据真实性经孕周冲突分析（15.2\%样本偏差<1周）确认；Beta分布假设通过GAMLSS残差正态性检验（p>0.05）；损失函数经敏感性分析（$\gamma=90\%-99\%$）验证稳健性；特征重要性通过SHAP分析确认（Z值贡献度>60\%）。相较传统假设（如高斯分布），本文假设更贴合数据特性，为后续建模提供坚实基础。建议在实际应用中进一步验证特征集的普适性。

% --- Chapter 4: Data Preprocessing and EDA ---
\section*{第四章 数据预处理与探索性分析}
\setcounter{section}{4}

在建模前，首要的是对原始数据的初步探查。附件数据集包含1687条检测记录，存在一些建模前需处理的问题，如同孕妇同日的重复测序、不一致的孕周记录、关键信息的缺失以及潜在的测序质量异常等。这些数据噪声若不加以处理，将严重干扰后续问题的解答和模型效果。

为此，本文构建了一套系统性的数据预处理与特征工程流程（见图4.1），意在将原始数据转化为结构清晰、无缺失极端值且特征增强的高质量分析集。该流程的亮点不仅在于创新性地设计了\textbf{差异化聚合规则}来处理同日内的技术重复，和基于\textbf{临床先验知识建立核心假设}以“裁决”孕周冲突，更在于我们基于文献审慎地处理了质量控制指标。我们认识到所给样本（高BMI孕妇）普遍存在GC含量低于常规40\%阈值的情况，因此避免了过度清洗导致的样本大量流失。

\begin{figure}[H] 
    \centering
    \includegraphics[width=0.8\textwidth]{数据预处理流程图.jpg}
    \caption{数据预处理流程图}
    \label{fig:4.1}
\end{figure}

最终，原始噪声数据被转化为包含1653条分层标记记录与39个特征维度的高质量分析集。该过程在保留关键样本信息的同时，通过对“数据噪声”的逆向利用，生成了多个具有分析价值的新特征（见表4.1），为后续的建模与推断奠定了坚实基础。

\begin{table}[H]
\centering
\caption{特征工程新增核心变量说明}
\label{tab:4.1}
\begin{tabular}{|c|c|c|}
\hline
\textbf{变量名} & \textbf{说明} & \textbf{生成方式} \\ \hline
Final\_Week & 标准化孕周 & 由J列字符串转换 \\ \hline
Data\_Conflict & 孕周记录冲突标志 & 计算J\_calc与Final\_Week差值 \\ \hline
Intra\_Day\_Var\_* & 各染色体Z值日内波动性 & 计算同日检测Z值标准差 \\ \hline
GC\_Abnormal & GC含量异常标志 & 判断GC含量是否超出常规范围 \\ \hline
Y\_Achieved\_Threshold & Y浓度达标标志 & 标记男胎Y染色体浓度是否达到4\%的可靠性阈值\\ \hline
\end{tabular}
\end{table}

\subsection*{4.1 数据整合与时序标准化}

预处理的首要步骤是合并数据集，本文将“男胎检测数据”与“女胎检测数据”进行垂直合并得1687条观测记录，并新增\texttt{胎儿性别}列以明确区分性别。

接着，本文针对部分异构格式进行了严格的标准化处理，旨在消除后续计算中的潜在误差，确保时间维度分析的准确性。

\subsubsection*{4.1.1 日期格式统一化}
原始数据中，\texttt{F}列（末次月经时间）和\texttt{H}列（检测时间）的日期格式存在不一致的情况，为解决这一问题，本文将这两列的格式统一转换为标准的\texttt{datetime}格式。

\subsubsection*{4.1.2 孕周数据数值化}
将\texttt{J}列（孕周）采用的是“周+天”（如“11w+6”）的文本格式，无法直接应用于数学模型。因此，我们依据以下公式将其转化为连续的数值型变量：
\begin{equation}
\text{Final\_Week} = \text{周数} + \frac{\text{天数}}{7}
\end{equation}
并存储于新列，为后续的回归分析、关系探索等建模工作提供了关键的连续型自变量。

\subsection*{4.2 重复观测值的差异化处理与特征工程}
在数据集中，孕妇检测记录可分为两类：\textbf{“同日多次检测”与“异日多次检测”}。前者主要反映了单次采血或同日采血的技术性重复与测量误差，而后者则记录了孕妇在不同孕周的真实生理变化轨迹。若采用统一的聚合或删除方法，将会丢失纵向动态信息或引入偏差。

为解决这一挑战，我们设计了一套基于指标性质的\textbf{三层差异化聚合规则}，旨在精确剔除技术冗余的同时，完整保留生理变化信息，并从技术误差中提炼新的特征。

\subsubsection*{4.2.1 定义三层差异化聚合规则}
我们依据各变量的临床与统计学意义，将其划分为三个类别，并分别定制了聚合策略：
\begin{table}[H]
\centering
\caption{针对同日重复记录的三层差异化聚合规则}
\label{tab:4.2}
\begin{tabular}{|c|c|c|}
\hline
\textbf{类别} & \textbf{包含变量示例} & \textbf{聚合策略} \\ \hline
诊断性指标 & 各染色体Z值、异常判定 & 取绝对值最大（保留最坏情况） \\ \hline
连续性/质量指标 & GC含量、读段数 & 取中位数 \\ \hline
固定信息 & 年龄、BMI、身高、体重 & 取第一个值 \\ \hline
\end{tabular}
\end{table}

\subsubsection*{4.2.2 逆向特征工程构建日内波动性等新特征}
在聚合消除技术误差的同时，本文认为同日内的数据波动本身也蕴含着关于检测稳定性的信息。为此，我们进行了逆向特征工程：
在原始的、未聚合的同日分组数据上，我们为\texttt{Q}至\texttt{U}列的每个诊断性Z值计算了其\textbf{组内标准差}，并生成了\texttt{Intra\_Day\_Var\_13}至\texttt{Intra\_Day\_Var\_Y}等5个新特征，用以量化各染色体Z值的\textbf{日内波动性}。

这一操作将原始数据中的“技术噪声”转化为一个可度量的、反映测序过程稳定性的新指标。对于当日仅有单条记录的样本，其波动性为0，保证了该特征的逻辑一致性。

通过执行上述差异化聚合与特征工程，数据集1687条精简至1653条，有效剔除了技术性重复。并且还额外获得了5个描述检测稳定性的创新特征，丰富了数据集的特征信息维度。

\subsection*{4.3 核心假设与孕周冲突处理}
孕周（\texttt{Final\_Week}）是本研究中探究Y染色体浓度变化规律的最核心自变量，其准确性直接关系到模型的成败。在数据处理中，本文发现通过\texttt{H}列（检测时间）与\texttt{F}列（末次月经）计算出的孕周（记为\texttt{J\_calc}）与附件中直接提供的\texttt{J}列孕周记录存在冲突。具体而言，约15.2\%（251/1653）的样本其两者差异超过一周。

\subsubsection*{4.3.1 确立以临床记录为基准的核心假设}
\textbf{核心假设：附件中所提供的\texttt{J}列（孕妇本次检测时的孕周）是经过临床校正的，其准确性优先级高于依赖孕妇记忆的LMP（末次月经）计算法，应被视为最接近临床诊断的“金标准”。}

该假设基于以下理由：
\begin{itemize}
    \item \textbf{信息来源的可靠性}：\texttt{J}列的数值很可能综合了B超等更精确的临床检查结果，符合临床通行做法。
    \item \textbf{LMP计算的局限性}：\texttt{F}列（末次月经）依赖孕妇记忆，存在不确定性，且不适用于IVF等特殊妊娠情况。
\end{itemize}

因此，以\texttt{J}列转换得到的\texttt{Final\_Week}为金标准，基于\texttt{F}和\texttt{H}列的计算仅用于验证和一致性分析。

\subsubsection*{4.3.2 冲突标记与特征化}
基于上述假设，处理流程如下：
\begin{enumerate}
    \item \textbf{计算验证孕周}：利用标准化日期数据计算验证孕周\texttt{J\_calc}：
    \begin{equation}
    \text{J\_calc} = \frac{(\text{检测时间H} - \text{末次月经时间F}).\text{days}}{7}
    \end{equation}
    \item \textbf{创建冲突标签}：生成布尔型特征\texttt{Data\_Conflict}，若\texttt{J\_calc}与\texttt{Final\_Week}差值大于1周，标记为\texttt{True}，否则为\texttt{False}。
\end{enumerate}
此流程确保了\texttt{Final\_Week}的可靠性，并将冲突转化为新特征，为后续分析奠定基础。

\subsection*{4.4 缺失值填补与质量控制}

\subsubsection*{4.4.1 缺失值的反算填补}
数据排查发现5条末次月经时间缺失（A108、A139、A159、B034、B109），采用反算方法填补：
\begin{equation}
\text{末次月经时间F} \approx \text{检测时间H} - (\text{Final\_Week} \times 7) \text{天}
\end{equation}

\subsubsection*{4.4.2 基于样本特性的质量控制策略}
GC含量常规范围为40\%–60\%，但数据集中40.4\%样本低于40\%。直接剔除会导致样本丢失，结合高BMI孕妇特性，设计两步质量控制：
\begin{enumerate}
    \item \textbf{质量分层标记}：创建\texttt{GC\_Abnormal}特征，标记GC含量超出[40\%, 60\%]的样本，用于后续分析。
    \item \textbf{极端离群点过滤}：仅删除GC含量<35\%或>62\%的样本，最终无记录删除。
\end{enumerate}

\subsection*{4.5 数据预处理成果总结}
最终数据集包含1653条记录、39个特征维度，关键统计见表4.3。相比原始数据，精炼数据集提高了分析质量，为建模提供了坚实基础。建议在实际应用中进一步验证GC含量阈值的适用性。

\begin{table}[H]
    \centering
    \caption{精炼数据集关键特征一览}
    \label{tab:4.3}
    \begin{tabular}{|c|c|c|}
        \hline
        \textbf{类别} & \textbf{指标} & \textbf{统计值} \\
        \hline
        \textbf{整体规模} & 最终样本量 & 1653条记录 \\
        & 最终特征维度 & 39个 \\
        & 独立孕妇个体数 & 414名 \\
        \hline
        \textbf{样本构成} & 男胎样本数 (占比) & 1063条 (64.3\%) \\
        & 女胎样本数 (占比) & 590条 (35.7\%) \\
        \hline
        \textbf{核心变量} & 平均孕周 (周) & 17.35 (SD=4.24) \\
        & 平均BMI & 32.23 (SD=2.97) \\
        & 男胎平均Y浓度 & 7.75\% (SD=3.34\%) \\
        \hline
        \textbf{质量特征} & 孕周记录冲突比例 & 15.2\% \\
        & GC含量偏离常规比例 & 40.4\% \\
        & Y浓度达标比例 (男胎) & 86.8\% (923/1063) \\
        \hline
    \end{tabular}
\end{table}

% --- Chapter 5: Problem 1 Modeling ---
\section*{第五章 问题一的模型建立与求解：Y染色体浓度的非线性与异方差性分析}
\setcounter{section}{5}

本章采用由浅入深的多阶段建模策略：首先通过探索性数据分析（EDA）揭示数据内在的复杂性，然后构建广义可加模型（GAM）以捕捉核心的非线性平均趋势，最终引入能够联合建模均值与方差的GAMLSS框架，以完整刻画Y染色体浓度的动态变化规律。

\subsection*{5.1 探索性数据分析：揭示变量间的复杂关系}

\subsubsection*{5.1.1 线性相关性勘探}
我们首先计算了各关键变量间的皮尔逊（Pearson）相关系数，并将其可视化为热力图（图5.1）。结果显示，Y染色体浓度与\texttt{Final\_Week}（孕周）、\texttt{孕妇BMI}及\texttt{年龄}的皮尔逊相关系数分别为0.129、-0.152和-0.115，均未超过0.2，表明线性关系微弱，提示需采用非线性模型。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{热力图.png}
    \caption{关键指标相关性热力图}
    \label{fig:5.1}
\end{figure}

\subsubsection*{5.1.2 非线性趋势与波动性的可视化}
通过分箱处理孕周，计算Y浓度均值，绘制平滑曲线（图5.2），揭示Y浓度随孕周呈现非单调、多阶段波动（12–17.5周上升，17.5–22.5周下降，22.5周后回升），无法用线性或S型模型拟合。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{binned_average_trend.png}
    \caption{Y染色体浓度随孕周的变化趋势图}
    \label{fig:mean_curve}
\end{figure}

\subsubsection*{5.1.3 异方差性的识别}
Y浓度与孕周的散点图（图5.3）显示“喇叭口”形态，方差随孕周增大，表明显著异方差性，传统线性回归不适用。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{Y染色体浓度与孕周关系散点图.png}
    \caption{Y染色体浓度 vs 孕周关系散点图}
    \label{fig:scatter_plot}
\end{figure}

\subsubsection*{5.1.4 非线性关系的量化}
斯皮尔曼相关系数显示Y浓度与孕周（0.0891，p=0.004）、BMI（-0.1572，p<0.001）、年龄（-0.1158，p<0.001）均显著相关，证实非线性关系的重要性。

\begin{table}[H]
    \centering
    \caption{Y染色体浓度与关键变量的斯皮尔曼相关性分析}
    \label{tab:5.1}
    \begin{tabular}{|c|c|c|}
        \hline
        变量名 & 斯皮尔曼系数 & P值 \\
        \hline
        孕周 & 0.0891 & 0.004 \\
        孕妇BMI & -0.1572 & <0.001 \\
        年龄 & -0.1158 & <0.001 \\
        \hline
    \end{tabular}
\end{table}

\subsubsection*{5.1.5 其他关键变量的关联探索}
图5.4显示：（a）BMI与Y浓度负相关，高BMI降低高浓度样本频率；（b）IVF妊娠Y浓度中位数低于自然受孕；（c）年龄影响较弱。这些结果为变量选择提供依据。

\begin{figure}[H]
    \centering
    \begin{minipage}{0.8\textwidth}
        \centering
        \includegraphics[width=\textwidth]{Y染色体浓度与BMI关系散点图.png}
        \caption{(a)}
        \label{fig:first_plot}
    \end{minipage}
    
    \vspace{0.5cm}
    \begin{minipage}{0.8\textwidth}
        \centering
        \includegraphics[width=\textwidth]{IVF妊娠与Y染色体浓度箱线图.png}
        \caption{(b)}
        \label{fig:second_plot}
    \end{minipage}
    
    \vspace{0.5cm}
    \begin{minipage}{0.8\textwidth}
        \centering
        \includegraphics[width=\textwidth]{Y染色体浓度与年龄关系散点图.png}
        \caption{(c)}
        \label{fig:third_plot}
    \end{minipage}
    \caption{其他关键变量关系探索}
    \label{fig:combined_plot}
\end{figure}

\subsection*{5.2 基线模型：广义可加模型（GAM）}

\subsubsection*{5.2.1 模型介绍与选择依据}
GAM通过非参数平滑函数捕捉非线性关系，适合本研究中Y浓度的复杂趋势。相较线性回归（R²<0.05），GAM更灵活，保持可解释性。

\subsubsection*{5.2.2 模型构建与求解}
GAM模型公式为：
\begin{equation}
E(Y_{cffDNA}) = \beta_0 + te(\text{Final\_Week}, \text{孕妇BMI}) + s(\text{年龄})
\end{equation}
使用\texttt{pygam}库拟合，\texttt{te()}捕捉孕周-BMI交互，\texttt{s()}拟合年龄非线性效应。

\subsubsection*{5.2.3 模型结果与分析}
\paragraph{模型整体性能}
GAM的皮尔逊相关系数为0.3758（p<0.001），斯皮尔曼系数为0.3525，R²=0.1388，RMSE=0.0310，优于线性回归（R²=0.045）。

\begin{table}[H]
    \centering
    \caption{GAM模型整体性能评估}
    \label{tab:5.2}
    \begin{tabular}{|c|c|}
        \hline
        指标 & 数值 \\
        \hline
        传统 R² & 0.1388 \\
        预测相关性 (Pearson) & 0.3758 (p < 0.001) \\
        均方根误差 (RMSE) & 0.0310 \\
        平均绝对误差 (MAE) & 0.0250 \\
        \hline
    \end{tabular}
\end{table}

\paragraph{变量效应分析}
孕周-BMI交互项（p<0.001，EDoF=12.1）和年龄平滑项（p<0.001，EDoF=5.6）均显著，证实复杂非线性关系。

\paragraph{局限性与改进方向}
GAM残差诊断（图5.5）显示异方差性，需GAMLSS模型解决。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{gam_model_diagnostics.png}
    \caption{GAM模型残差诊断图}
    \label{fig:gam_diagnostics}
\end{figure}

\subsection*{5.3 位置、尺度、形状的广义可加模型（GAMLSS）}

\subsubsection*{5.3.1 模型介绍与选择依据}
GAMLSS通过联合建模均值和方差，克服GAM的异方差性局限，选用Beta分布适配Y浓度（0–1）。相较GAM（AIC=-4300），GAMLSS更精确。

\subsubsection*{5.3.2 模型构建与求解}
均值和方差子模型为：
\begin{align}
\log\left(\frac{1 - \mu}{\mu}\right) &= s(\text{Final\_Week}) + s(\text{孕妇BMI}) + s(\text{年龄}) + \beta_{IVF} \\
\log(\sigma) &= s(\text{Final\_Week}) + \beta_{BMI} \cdot \text{孕妇BMI}
\end{align}
使用R的\texttt{gamlss}包拟合1063例男胎数据。

\subsubsection*{5.3.3 模型结果与分析}
\paragraph{模型整体性能}
GAMLSS的AIC=-4422，BIC=-4317.7，广义R²=0.1404，CV-RMSE=0.0317，预测覆盖率95.77\%，优于GAM。

\begin{table}[H]
    \centering
    \caption{GAMLSS模型整体性能评估}
    \label{tab:5.3}
    \begin{tabular}{|c|c|c|}
        \hline
        指标 & 数值 & 描述 \\
        \hline
        AIC & -4422.0 & 赤池信息准则，越小越好 \\
        BIC & -4317.7 & 贝叶斯信息准则，越小越好 \\
        广义 R² & 0.1404 & 模型的整体解释力 \\
        CV-RMSE & 0.0317 & 5折交叉验证均方根误差 \\
        预测覆盖率 & 95.77\% & 95\%预测区间的覆盖率 \\
        \hline
    \end{tabular}
\end{table}

\paragraph{核心发现}
孕周、BMI、年龄、IVF妊娠对均值显著（p<0.05），孕周对方差显著（p=0.00195），BMI不显著（p=0.556）。

\begin{figure}[H]
    \centering
    \begin{subfigure}{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{mu_final_week_effect.png}
        \caption{孕周对Y浓度均值的偏效应图}
        \label{fig:mu_effect}
    \end{subfigure}
    \hfill
    \begin{subfigure}{0.45\textwidth}
        \centering
        \includegraphics[width=\textwidth]{sigma_final_week_effect.png}
        \caption{孕周对Y浓度方差的偏效应图}
        \label{fig:sigma_effect}
    \end{subfigure}
    \caption{孕周对Y浓度均值与方差的偏效应图}
    \label{fig:mu_sigma_effect}
\end{figure}

\paragraph{模型诊断与可视化}
GAMLSS通过残差正态性检验（p>0.05），分位数预测曲线（图5.7）显示预测区间随孕周扩大，量化了不确定性。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{gamlss_centile_curves.png}
    \caption{GAMLSS分位数预测曲线图}
    \label{fig:gamlss_centile}
\end{figure}

\subsection*{5.4 模型比较与问题一总结}

\subsubsection*{5.4.1 模型比较}
GAMLSS（AIC=-4422）优于GAM（AIC=-4300）和线性回归（AIC≈-4200），预测覆盖率95.77\%验证其稳健性。

\subsubsection*{5.4.2 问题一结论}
GAMLSS模型揭示Y浓度非线性与异方差特性，孕周驱动均值与方差，BMI等负相关，p值<0.05，AIC=-4422，覆盖率95.77\%。相较传统模型，GAMLSS更精确，为时点优化提供基础。建议临床参考GAMLSS预测的Y浓度分布，优化检测时机。

% --- Chapter 6: Optimal Timing Decision ---
\section*{第六章 基于风险最小化的NIPT最优时点决策与优化}
\setcounter{section}{6}

\subsection*{6.1 问题二：基于核心因素的基准决策模型}

\subsubsection*{6.1.1 简化预测引擎的构建}
采用简化GAMLSS模型（仅含孕周和BMI），公式为：
\begin{align}
\mu &: Y_{cffDNA} \sim cs(\text{Final\_Week}) + cs(\text{孕妇BMI}) \\
\sigma &: \sim cs(\text{Final\_Week})
\end{align}
AIC=-4409，p<0.001，优于线性模型。

\subsubsection*{6.1.2 基于混合模型的稳健决策曲线}
在BMI<28区间采用分位数回归，BMI≥28使用GAMLSS，拼接平滑决策曲线（图6.1），提高低BMI预测稳定性。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{hybrid_curve_comparison_combined.png}
    \caption{混合最优时点曲线的构建过程}
    \label{fig:hybrid_curve}
\end{figure}

\subsubsection*{6.1.3 基于损失函数的时点优化}
引入损失函数：
\begin{equation}
L(w) = C_{fail} \cdot P_{fail}(w) + C_{delay}(w)
\end{equation}
其中$C_{fail}=100$，$P_{fail}(w)$为Y浓度<4\%概率，$C_{delay}(w)$随孕周非线性增长。优化结果见图6.2。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{loss_function_optimization_example.png}
    \caption{损失函数优化示例 (BMI=30.8)}
    \label{fig:loss_function}
\end{figure}

\subsubsection*{6.1.4 基准推荐方案与局限性}
推荐方案（表6.1）将检测时点提前10–16周，成功率56.9\%–84.9\%，优于传统23–30周。局限性在于未考虑年龄、IVF等因素。

\begin{table}[H]
\centering
\caption{问题二：基于BMI的NIPT最优时点基准推荐方案}
\label{tab:q2}
\begin{tabular}{cccccc}
\toprule
分组 & BMI区间 & 最优孕周 & 传统孕周 & 改进 & 成功率 \\
\midrule
1 & [20,27)   & 13.2 & 23.4 & +10.2 & 75.8\% \\
2 & [27.5,35) & 13.5 & 25.8 & +12.2 & 84.9\% \\
3 & [35.5,40) & 13.5 & 28.5 & +15.0 & 72.5\% \\
4 & [40.5,47) & 13.8 & 30.0 & +16.2 & 56.9\% \\
\bottomrule
\end{tabular}
\end{table}

\subsection*{6.2 问题三：综合多因素的优化决策模型}

\subsubsection*{6.2.1 预测引擎的升级}
采用完全版GAMLSS（含孕周、BMI、年龄、IVF），AIC=-4422，优于简化版。

\subsubsection*{6.2.2 推荐方案的校正}
优化后方案（表6.2）调整了高BMI组时点，更加精准。

\begin{table}[H]
\centering
\caption{基准方案（问题二）与优化方案（问题三）对比}
\label{tab:q3_compare}
\begin{tabular}{cccc}
\toprule
分组 & BMI区间 & 基准孕周 & 优化孕周 \\
\midrule
1 & <25      & 13.2 & 13.2 \\
2 & [25,30)  & 13.5 & 13.2 \\
3 & [30,35)  & 13.5 & 13.5 \\
4 & $\geq$35 & 13.8 & 14.0 \\
\bottomrule
\end{tabular}
\end{table}

\subsubsection*{6.2.3 误差与敏感性分析}
参数扰动分析给出95\%置信区间（图6.3），$\gamma=90\%–99\%$敏感性分析验证稳健性。

\begin{figure}[H]
    \centering
    \includegraphics[width=0.7\textwidth]{alternative_uncertainty_analysis.png}
    \caption{最终方案的不确定性与敏感性分析}
    \label{fig:uncertainty}
\end{figure}

\subsubsection*{6.2.4 最终优化推荐方案}
最终方案（表6.3）提供量化置信区间，适用于临床。

\begin{table}[H]
\centering
\caption{问题三：综合多因素的最终优化推荐方案}
\label{tab:q3_final}
\begin{tabular}{cccc}
\toprule
分组 & BMI区间 & 最优孕周 & 95\%置信区间 \\
\midrule
1 & <25      & 13.2 & [12.0,14.2] \\
2 & [25,30)  & 13.2 & [12.2,14.5] \\
3 & [30,35)  & 13.5 & [12.5,14.8] \\
4 & $\geq$35 & 14.0 & [12.8,15.2] \\
\bottomrule
\end{tabular}
\end{table}

\subsection*{6.3 总结}
通过损失函数与GAMLSS优化，推荐时点提前10–15周，优于传统方法。建议临床采纳分组方案，整合至CDSS。

% --- Chapter 7: Problem 4 ---
\section*{第七章 问题四的模型建立与求解：构建分层的NIPT智能诊断系统}
\setcounter{section}{7}

\subsection*{7.1 第一阶段：高灵敏度的“广谱”异常筛查模型}

\subsubsection*{7.1.1 建模目标与策略}
针对590例女胎样本（67例异常），设计高召回率逻辑回归模型，结合Z\_Max等衍生特征、5折交叉验证和动态阈值优化，优先降低漏诊风险。

\subsubsection*{7.1.2 模型结果与分析}
逻辑回归模型召回率95.6\%±5.8\%，优于随机森林（约85\%），重度肥胖组召回率100\%。

\subsection*{7.2 第二阶段：特异性的“精确制导”分型判定模型}

\subsubsection*{7.2.1 建模目标与策略}
采用一对多分类器（T13/T18/T21），优先使用特异性特征，解决样本稀缺问题。

\subsubsection*{7.2.2 模型结果与分析}
T18检测器召回率78.4\%，可靠性高；T13/T21受样本限制，召回率较低（表7.1）。

\begin{table}[H]
    \centering
    \caption{染色体特异性分型检测器性能}
    \label{tab:specific_models}
    \begin{tabular}{lcccc}
        \toprule
        \textbf{检测器} & \textbf{目标异常} & \textbf{样本量 (例)} & \textbf{平均召回率 (±SD)} & \textbf{模型可靠性} \\
        \midrule
        \textbf{T18检测器} & 18三体 & 46 & \textbf{78.4\% $\pm$ 11.6\%} & \textbf{高} \\
        \textbf{T13检测器} & 13三体 & 23 & \textbf{66.0\% $\pm$ 21.5\%} & 中等 \\
        \textbf{T21检测器} & 21三体 & 13 & \textbf{20.0\% $\pm$ 26.7\%} & 低 \\
        \bottomrule
    \end{tabular}
\end{table}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.8\textwidth]{chromosome_patterns_20250906_183751.png}
    \caption{染色体模式图}
    \label{fig:chromosome_patterns}
\end{figure}

\subsection*{7.3 最终判定方法与讨论}
两阶段流程（高召回率筛查+特异性分型）兼顾灵敏度与精度，优于单一多分类模型。建议临床采纳T18检测器，T13/T21需更多数据验证。

% --- Chapter 8: Model Evaluation and Promotion ---
\section*{第八章 模型评价与推广}
\setcounter{section}{8}

\subsection*{8.1 模型优点}
\begin{itemize}
    \item \textbf{方法学创新}：GAMLSS联合建模均值与方差（AIC=-4422），优于传统GAM（AIC=-4300）；损失函数优化时点降低10–15周风险；两阶段诊断流程召回率95.6\%，解决不平衡问题。
    \item \textbf{稳健性}：5折交叉验证（覆盖率95.77\%）和敏感性分析验证模型可靠性。
    \item \textbf{应用价值}：提供个性化检测时点（表6.3）和异常诊断路径（7.3节），提升NIPT效率。
\end{itemize}

\subsection*{8.2 模型缺点}
\begin{itemize}
    \item \textbf{数据局限}：T21/T13样本稀少（13/23例）限制分型精度；高BMI样本可能降低普适性。
    \item \textbf{假设依赖}：损失函数权重需临床进一步验证。
\end{itemize}

\subsection*{8.3 模型推广}
\begin{itemize}
    \item \textbf{框架普适性}：GAMLSS+损失函数框架可推广至其他生物标志物决策。
    \item \textbf{诊断借鉴}：两阶段流程适用于罕见病检测。
    \item \textbf{CDSS潜力}：建议整合模型至医院信息系统，提升NIPT标准化。
\end{itemize}

% --- 附录 ---
\appendix
\section*{附录}

\subsection*{A.1 补充图表}
\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\textwidth]{gamlss_diagnostic_plots.png}
    \caption{GAMLSS模型残差正态性检验图}
    \label{fig:appendix_A}
\end{figure}

\begin{figure}[H]
    \centering
    \includegraphics[width=0.6\textwidth]{gamlss_worm_plot.png}
    \caption{GAMLSS模型Worm Plot残差正态性检验}
    \label{fig:appendix_B}
\end{figure}

\subsection{A.2 核心算法伪代码}
为清晰展示本文关键环节的实现逻辑，我们将核心的数据预处理、最优时点决策及女胎异常判定算法流程概括如下。

\begin{algorithm}[H]
    \caption{数据预处理：差异化聚合与特征工程}
    \label{alg:preprocessing}
    \begin{algorithmic}[1]
        \Require 原始NIPT数据集 $D_{raw}$
        \Ensure 精炼数据集 $D_{clean}$
        
        \State $D_{merged} \gets \text{Merge}(\text{男胎数据}, \text{女胎数据})$ \Comment{合并数据并添加性别标识}
        \State $D_{std} \gets \text{StandardizeDatesAndWeeks}(D_{merged})$ \Comment{标准化日期与孕周格式}
        
        \State 按“孕妇代码”和“检测日期”对 $D_{std}$ 进行分组 $G$
        \State $D_{aggregated} \gets \text{InitializeEmptyDataFrame}()$
        
        \For{每个分组 $g \in G$}
            \State \Comment{应用三层差异化聚合规则}
            \State $z\_vals \gets \text{AggregateByMaxAbsolute}(g.\text{Z\_Values})$ \Comment{规则A：保留最坏风险}
            \State $quality\_metrics \gets \text{AggregateByMedian}(g.\text{Quality\_Metrics})$ \Comment{规则B：计算稳健中心}
            \State $static\_info \gets \text{AggregateByFirst}(g.\text{Static\_Info})$ \Comment{规则C：保留固定信息}
            
            \State \Comment{逆向特征工程：量化技术噪声}
            \State $intra\_day\_vars \gets \text{CalculateStdDev}(g.\text{Z\_Values})$ \Comment{构建日内波动性特征}
            
            \State $row_{new} \gets \text{Combine}(z\_vals, quality\_metrics, static\_info, intra\_day\_vars)$
            \State $D_{aggregated}.\text{AddRow}(row_{new})$
        \EndFor
        
        \State $D_{clean} \gets \text{CreateQualityFlagsAndFilter}(D_{aggregated})$ \Comment{创建质量标签并过滤极端值}
        \State \Return $D_{clean}$
    \end{algorithmic}
\end{algorithm}

\begin{algorithm}[H]
    \caption{问题二、三：基于风险最小化的最优时点决策}
    \label{alg:decision_making}
    \begin{algorithmic}[1]
        \Require 孕妇个体特征 (如 $BMI, Age, IVF$); 完整版GAMLSS模型 $M_{GAMLSS}$
        \Ensure 最优推荐孕周 $w_{optimal}$ 及95\%置信区间
        
        \State \textbf{Function} PredictSuccessProbability($w, \text{features}, M$)
            \State $(\mu, \sigma) \gets M.\text{predict}(w, \text{features})$ \Comment{用GAMLSS预测均值和方差}
            \State $P_{success} \gets 1 - \text{CDF}_{\text{Beta}}(0.04 \mid \mu, \sigma)$ \Comment{计算Y浓度$\geq$4\%的概率}
            \State \Return $P_{success}$
        \EndFunction
        
        \State \textbf{Function} TotalLoss($w, \text{features}, M$)
            \State $P_{fail} \gets 1 - \text{PredictSuccessProbability}(w, \text{features}, M)$
            \State $L_{fail} \gets C_{fail} \cdot P_{fail}$ \Comment{计算检测失败损失}
            \State $L_{delay} \gets C_{delay}(w)$ \Comment{计算延误诊断损失}
            \State \Return $L_{fail} + L_{delay}$
        \EndFunction
        
        \State \Comment{通过数值优化求解总损失的最小值}
        \State $w_{optimal} \gets \arg\min_{w \in [10, 28]} \text{TotalLoss}(w, \text{features}, M_{GAMLSS})$
        
        \State \Comment{通过参数扰动法进行误差分析}
        \State $CI \gets \text{ParameterPerturbationAnalysis}(w_{optimal}, \text{features}, M_{GAMLSS})$
        \State \Return $w_{optimal}, CI$
    \end{algorithmic}
\end{algorithm}

\begin{algorithm}[H]
    \caption{问题四：两阶段女胎异常判定}
    \label{alg:diagnostic_flow_appendix}
    \begin{algorithmic}[1]
        \Require 女胎样本特征 $X_{patient}$; 筛查模型 $M_{screen}$; 特异性检测器 $M_{T18}, M_{T13}, M_{T21}$
        \Ensure 最终诊断建议 $D_{final}$ 及各分型置信度
        
        \State \Comment{--- 第一阶段：高灵敏度“广谱”筛查 ---}
        \State $P_{abnormal} \gets M_{screen}.\text{predict\_proba}(X_{patient})$
        \State $threshold_{dynamic} \gets \text{CalculateDynamicThreshold}(X_{patient})$
        
        \If{$P_{abnormal} < threshold_{dynamic}$}
            \State $D_{final} \gets$ "未见明显异常"
            \State \Return $D_{final}$
        \Else
            \State \Comment{--- 第二阶段：特异性分型判定 ---}
            \State $P_{T18} \gets M_{T18}.\text{predict\_proba}(X_{patient})$
            \State $P_{T13} \gets M_{T13}.\text{predict\_proba}(X_{patient})$
            \State $P_{T21} \gets M_{T21}.\text{predict\_proba}(X_{patient})$
            \State $D_{final} \gets \text{GenerateReport}(P_{T18}, P_{T13}, P_{T21})$ \Comment{综合各检测器置信度生成报告}
            \State \Return $D_{final}$
        \EndIf
    \end{algorithmic}
\end{algorithm}

\begin{thebibliography}{99}
\bibitem{ref1} Lo, Y. M. D. et al. Noninvasive Prenatal Testing for Fetal Aneuploidy. \textit{New England Journal of Medicine}, 2012, 367(17): 1587–1595.
\bibitem{ref2} Rigby, R. A., Stasinopoulos, D. M. Generalized Additive Models for Location, Scale and Shape. \textit{Journal of the Royal Statistical Society: Series C}, 2005, 54(3): 507–554.
\end{thebibliography}

\end{document}
```